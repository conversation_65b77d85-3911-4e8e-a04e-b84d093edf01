#!/usr/bin/env python3
"""
使用 Optuna 优化 SuperTrend 参数
自动寻找最佳的 ATR 周期和倍数组合以提高回测表现
"""

import optuna
import pandas as pd
import numpy as np
import json
import argparse
import os
import sys
import sqlite3
from datetime import datetime, timedelta
import warnings
from typing import Dict, Any, Optional, Tuple
import logging

# 导入回测相关模块
from backtest_money_quick import (
    HistoricalBacktester, load_data_from_sqlite, load_supertrend_data,
    parse_time_input, load_chushou_config
)

try:
    from model_utils_815 import get_coin_config, calculate_features
except ImportError:
    def get_coin_config(coin): 
        return {'model_basename': f"{coin.lower()}_model", 'api_symbol': coin.upper() + 'USDT'}
    def calculate_features(df, timeframe): 
        return df

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 抑制一些警告
warnings.filterwarnings('ignore', category=UserWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

class SuperTrendOptimizer:
    """SuperTrend 参数优化器"""
    
    def __init__(self, 
                 coin: str = "ETH",
                 interval: str = "5m", 
                 market: str = "spot",
                 db_path: str = "coin_data.db",
                 model_file: str = None,
                 config_file: str = None,
                 start_time: Optional[pd.Timestamp] = None,
                 end_time: Optional[pd.Timestamp] = None,
                 initial_capital: float = 1000.0,
                 risk_per_trade: float = 2.0,
                 max_active_predictions: int = 5,
                 chushou_file: Optional[str] = None):
        
        self.coin = coin
        self.interval = interval
        self.market = market
        self.db_path = db_path
        self.start_time = start_time
        self.end_time = end_time
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_active_predictions = max_active_predictions
        self.chushou_file = chushou_file
        
        # 获取币种配置
        coin_config = get_coin_config(coin)
        self.api_symbol = coin_config['api_symbol']
        
        # 设置模型文件路径
        self.model_file = model_file or f"models/{coin_config['model_basename']}_model.joblib"
        self.config_file = config_file or f"models/{coin_config['model_basename']}_config.json"
        
        # 验证文件存在
        if not os.path.exists(self.model_file):
            raise FileNotFoundError(f"模型文件不存在: {self.model_file}")
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        # 加载主要回测数据
        logger.info(f"加载回测数据: {self.api_symbol} {interval}")
        self.main_df = load_data_from_sqlite(
            db_path, self.api_symbol, interval, market, 1.0, start_time, end_time
        )
        
        if self.main_df is None or self.main_df.empty:
            raise ValueError("无法加载回测数据")
        
        logger.info(f"回测数据加载完成: {len(self.main_df)} 条记录")
        
        # 预计算特征（避免重复计算）
        logger.info("预计算特征数据...")
        with open(self.config_file, 'r') as f:
            self.model_config = json.load(f)
        
        self.features_df = calculate_features(self.main_df, self.model_config['timeframe_minutes'])
        logger.info("特征计算完成")
        
        # 加载时间过滤配置
        self.time_filter = load_chushou_config(chushou_file) if chushou_file else None
        
        # 统计信息
        self.trial_count = 0
        self.best_result = None
        
    def run_single_backtest(self, 
                           supertrend_interval: str,
                           atr_period: int, 
                           multiplier: float,
                           silent: bool = True) -> Dict[str, Any]:
        """运行单次回测"""
        
        try:
            # 加载 SuperTrend 数据
            supertrend_df = load_supertrend_data(
                self.db_path, self.api_symbol, supertrend_interval, self.market,
                self.start_time, self.end_time, atr_period, multiplier
            )
            
            if supertrend_df is None:
                return {'error': 'SuperTrend数据加载失败'}
            
            # 创建回测器
            backtester = HistoricalBacktester(
                self.model_file, self.config_file, 
                self.initial_capital, self.risk_per_trade, 1.0, None,
                supertrend_df, True  # 启用 SuperTrend 过滤
            )
            
            # 找到有效的特征数据起始点
            valid_features_df = self.features_df.dropna(subset=self.model_config['feature_list'])
            if valid_features_df.empty:
                return {'error': '无有效特征数据'}
            
            first_valid_index_pos = self.main_df.index.get_loc(valid_features_df.index[0])
            
            # 运行回测
            for i in range(first_valid_index_pos, len(self.main_df)):
                current_timestamp = self.main_df.index[i]
                current_price = self.main_df.iloc[i]['close']
                
                # 检查现有预测
                backtester.check_predictions(current_price, current_timestamp, i)
                
                # 检查是否可以新增预测
                active_count = len([p for p in backtester.active_predictions.values() if p['status'] == 'active'])
                if active_count < self.max_active_predictions:
                    if current_timestamp in self.features_df.index:
                        latest_features_series = self.features_df.loc[current_timestamp]
                        guess, probability, pred_price = backtester.make_prediction_from_features(latest_features_series)
                        
                        if guess is not None:
                            # 检查时间过滤
                            from backtest_money_quick import is_good_time_to_trade, get_supertrend_signal
                            if is_good_time_to_trade(current_timestamp, self.time_filter):
                                supertrend_signal = get_supertrend_signal(current_timestamp, backtester.supertrend_df)
                                backtester.add_prediction(guess, probability, pred_price, current_timestamp, i, supertrend_signal)
            
            # 结束所有活跃预测
            final_timestamp, final_price, final_idx = self.main_df.index[-1], self.main_df.iloc[-1]['close'], len(self.main_df) - 1
            for pred_id in list(backtester.active_predictions.keys()):
                backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
            
            # 计算结果指标
            final_capital = backtester.current_capital
            total_return = (final_capital - self.initial_capital) / self.initial_capital
            
            # 计算其他指标
            win_rate = backtester.successful_predictions / max(backtester.total_predictions, 1)
            
            # 计算最大回撤
            if backtester.completed_predictions:
                capital_series = [self.initial_capital]
                for pred in backtester.completed_predictions:
                    capital_series.append(pred['CapitalAfter'])
                
                capital_df = pd.Series(capital_series)
                rolling_max = capital_df.expanding().max()
                drawdown = (capital_df - rolling_max) / rolling_max
                max_drawdown = drawdown.min()
            else:
                max_drawdown = 0.0
            
            # 计算更详细的性能指标
            sharpe_ratio = 0.0
            sortino_ratio = 0.0
            calmar_ratio = 0.0
            
            if backtester.completed_predictions:
                returns = [pred['Score'] for pred in backtester.completed_predictions]
                if len(returns) > 1:
                    returns_array = np.array(returns)
                    # 夏普比率
                    sharpe_ratio = np.mean(returns_array) / (np.std(returns_array) + 1e-8)
                    
                    # Sortino比率
                    downside_returns = returns_array[returns_array < 0]
                    if len(downside_returns) > 0:
                        downside_std = np.std(downside_returns)
                        if downside_std > 0:
                            sortino_ratio = np.mean(returns_array) / downside_std
                        else:
                            sortino_ratio = float('inf') if np.mean(returns_array) > 0 else 0.0
                    else:
                        sortino_ratio = float('inf') if np.mean(returns_array) > 0 else 0.0
                    
                    # Calmar比率
                    if abs(max_drawdown) > 0:
                        calmar_ratio = total_return / abs(max_drawdown)
                    else:
                        calmar_ratio = float('inf') if total_return > 0 else 0.0
            
            result = {
                'total_return': total_return,
                'final_capital': final_capital,
                'total_predictions': backtester.total_predictions,
                'successful_predictions': backtester.successful_predictions,
                'win_rate': win_rate,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                'filtered_predictions': backtester.supertrend_filtered_predictions,
                'atr_period': atr_period,
                'multiplier': multiplier,
                'supertrend_interval': supertrend_interval
            }
            
            if not silent:
                logger.info(f"ATR:{atr_period}, 倍数:{multiplier:.1f}, 收益:{total_return:.2%}, 胜率:{win_rate:.2%}, 预测数:{backtester.total_predictions}")
            
            return result
            
        except Exception as e:
            logger.error(f"回测失败 (ATR:{atr_period}, 倍数:{multiplier:.1f}): {e}")
            return {'error': str(e)}
    
    def objective(self, trial: optuna.Trial) -> float:
        """Optuna 优化目标函数"""
        
        self.trial_count += 1
        
        # 定义参数搜索空间
        atr_period = trial.suggest_int('atr_period', 5, 30)
        multiplier = trial.suggest_float('multiplier', 1.5, 5.0, step=0.1)
        
        # SuperTrend 时间间隔选择
        supertrend_intervals = ['5m', '15m', '30m', '1h']
        supertrend_interval = trial.suggest_categorical('supertrend_interval', supertrend_intervals)
        
        # 运行回测
        result = self.run_single_backtest(supertrend_interval, atr_period, multiplier, silent=True)
        
        if 'error' in result:
            return -999.0  # 惩罚错误的参数组合
        
        # 定义优化目标（可以根据需要调整权重）
        total_return = result['total_return']
        win_rate = result['win_rate']
        max_drawdown = result['max_drawdown']
        sharpe_ratio = result['sharpe_ratio']
        sortino_ratio = result.get('sortino_ratio', 0.0)
        calmar_ratio = result.get('calmar_ratio', 0.0)
        
        # 处理无穷大值
        sortino_ratio = min(sortino_ratio, 10.0) if sortino_ratio != float('inf') else 10.0
        calmar_ratio = min(calmar_ratio, 10.0) if calmar_ratio != float('inf') else 10.0
        
        # 综合评分（可以调整权重）
        score = (
            total_return * 0.3 +           # 总收益权重 30%
            win_rate * 0.2 +               # 胜率权重 20%
            (-max_drawdown) * 0.2 +        # 最大回撤权重 20%（负值，越小越好）
            sharpe_ratio * 0.1 +           # 夏普比率权重 10%
            sortino_ratio * 0.1 +          # Sortino比率权重 10%
            calmar_ratio * 0.1             # Calmar比率权重 10%
        )
        
        # 惩罚预测数量过少的情况
        if result['total_predictions'] < 5:
            score -= 0.5
        
        # 记录最佳结果
        if self.best_result is None or score > self.best_result.get('score', -999):
            self.best_result = result.copy()
            self.best_result['score'] = score
        
        # 每10次试验输出一次进度
        if self.trial_count % 10 == 0:
            logger.info(f"试验 {self.trial_count}: ATR={atr_period}, 倍数={multiplier:.1f}, "
                       f"间隔={supertrend_interval}, 得分={score:.4f}")
        
        return score
    
    def optimize(self, n_trials: int = 100, timeout: Optional[int] = None) -> Dict[str, Any]:
        """运行参数优化"""
        
        logger.info(f"开始 SuperTrend 参数优化")
        logger.info(f"币种: {self.coin}, 间隔: {self.interval}")
        logger.info(f"数据范围: {self.main_df.index[0]} 到 {self.main_df.index[-1]}")
        logger.info(f"优化试验数: {n_trials}")
        
        # 创建 Optuna 研究
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=42),
            pruner=optuna.pruners.MedianPruner()
        )
        
        # 运行优化
        study.optimize(
            self.objective, 
            n_trials=n_trials, 
            timeout=timeout,
            show_progress_bar=True
        )
        
        # 获取最佳参数
        best_params = study.best_params
        best_value = study.best_value
        
        logger.info(f"\n=== 优化完成 ===")
        logger.info(f"最佳得分: {best_value:.4f}")
        logger.info(f"最佳参数: {best_params}")
        
        # 使用最佳参数运行详细回测
        logger.info("\n运行最佳参数的详细回测...")
        best_result = self.run_single_backtest(
            best_params['supertrend_interval'],
            best_params['atr_period'], 
            best_params['multiplier'], 
            silent=False
        )
        
        # 整理结果
        optimization_result = {
            'best_params': best_params,
            'best_score': best_value,
            'best_backtest_result': best_result,
            'study': study,
            'total_trials': len(study.trials),
            'optimization_time': datetime.now().isoformat()
        }
        
        return optimization_result

def save_optimization_results(result: Dict[str, Any], output_file: str):
    """保存优化结果"""
    
    # 准备保存的数据（移除不能序列化的对象）
    save_data = {
        'best_params': result['best_params'],
        'best_score': result['best_score'],
        'best_backtest_result': result['best_backtest_result'],
        'total_trials': result['total_trials'],
        'optimization_time': result['optimization_time']
    }
    
    # 保存为 JSON
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(save_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"优化结果已保存到: {output_file}")
    
    # 保存试验历史为 CSV
    study = result['study']
    trials_df = study.trials_dataframe()
    csv_file = output_file.replace('.json', '_trials.csv')
    trials_df.to_csv(csv_file, index=False)
    logger.info(f"试验历史已保存到: {csv_file}")

def print_optimization_summary(result: Dict[str, Any]):
    """打印优化结果摘要"""
    
    best_params = result['best_params']
    best_backtest = result['best_backtest_result']
    
    print("\n" + "="*60)
    print("SuperTrend 参数优化结果摘要")
    print("="*60)
    
    print(f"\n📊 最佳参数:")
    print(f"  ATR 周期: {best_params['atr_period']}")
    print(f"  ATR 倍数: {best_params['multiplier']:.1f}")
    print(f"  时间间隔: {best_params['supertrend_interval']}")
    
    print(f"\n📈 回测表现:")
    print(f"  总收益率: {best_backtest['total_return']:.2%}")
    print(f"  最终资金: ${best_backtest['final_capital']:.2f}")
    print(f"  胜率: {best_backtest['win_rate']:.2%}")
    print(f"  最大回撤: {best_backtest['max_drawdown']:.2%}")
    print(f"  夏普比率: {best_backtest['sharpe_ratio']:.3f}")
    
    print(f"\n🔢 交易统计:")
    print(f"  总预测数: {best_backtest['total_predictions']}")
    print(f"  成功预测: {best_backtest['successful_predictions']}")
    print(f"  过滤预测: {best_backtest['filtered_predictions']}")
    
    print(f"\n⚙️ 优化信息:")
    print(f"  总试验数: {result['total_trials']}")
    print(f"  最佳得分: {result['best_score']:.4f}")
    print(f"  优化时间: {result['optimization_time']}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SuperTrend 参数优化")
    
    # 基础参数
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="5m", help="K线间隔，例如 5m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    
    # 时间范围
    parser.add_argument("--start-time", help="开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="结束时间(北京时间, YYYY-MM-DD HH:MM)")
    
    # 回测参数
    parser.add_argument("--initial-capital", type=float, default=1000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=2.0, help="单次交易风险比例(%)")
    parser.add_argument("--max-active-predictions", type=int, default=5, help="最大同时活跃预测数")
    
    # 模型文件
    parser.add_argument("--model-file", help="模型文件路径 (.joblib)")
    parser.add_argument("--config-file", help="配置文件路径 (.json)")
    
    # 优化参数
    parser.add_argument("--n-trials", type=int, default=100, help="优化试验次数")
    parser.add_argument("--timeout", type=int, help="优化超时时间(秒)")
    
    # 其他选项
    parser.add_argument("--chushou-file", help="出手时间配置文件路径")
    parser.add_argument("--output", help="结果输出文件路径")
    
    args = parser.parse_args()
    
    try:
        # 解析时间
        start_time = parse_time_input(args.start_time) if args.start_time else None
        end_time = parse_time_input(args.end_time) if args.end_time else None
        
        # 创建优化器
        optimizer = SuperTrendOptimizer(
            coin=args.coin,
            interval=args.interval,
            market=args.market,
            db_path=args.db,
            model_file=args.model_file,
            config_file=args.config_file,
            start_time=start_time,
            end_time=end_time,
            initial_capital=args.initial_capital,
            risk_per_trade=args.risk_per_trade,
            max_active_predictions=args.max_active_predictions,
            chushou_file=args.chushou_file
        )
        
        # 运行优化
        result = optimizer.optimize(n_trials=args.n_trials, timeout=args.timeout)
        
        # 输出结果
        print_optimization_summary(result)
        
        # 保存结果
        output_file = args.output or f"supertrend_optimization_{args.coin}_{args.interval}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        save_optimization_results(result, output_file)
        
        # 生成使用建议
        best_params = result['best_params']
        print(f"\n💡 使用建议:")
        print(f"python backtest_money_quick.py \\")
        print(f"    --coin {args.coin} --interval {args.interval} \\")
        print(f"    --use-supertrend \\")
        print(f"    --supertrend-interval {best_params['supertrend_interval']} \\")
        print(f"    --supertrend-atr-period {best_params['atr_period']} \\")
        print(f"    --supertrend-multiplier {best_params['multiplier']:.1f} \\")
        print(f"    --quick")
        
    except Exception as e:
        logger.error(f"优化失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()