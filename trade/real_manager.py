# real_manager.py

import pandas as pd
import logging
from datetime import datetime, timezone
from typing import Dict, Optional
import platform
import os
import json
import pytz
from database_manager import DatabaseManager
from order_manager import OrderManager, OrderSide, OrderType, OrderStatus

class PortfolioManager:
    """
    资金管理模块 (V3 - Position First Logic)
    - Creates a position record first as an "intent to trade".
    - Links entry and exit orders to the position.
    - Uses detailed statuses (pending_open, active, open_failed, closing, completed) to track the lifecycle.
    - Includes retry logic for failed closing orders.
    """
    def __init__(self, config: Dict, logger: logging.Logger, display_name: str,
                 local_tz: pytz.BaseTzInfo, enable_sound: bool = True, db_manager: DatabaseManager = None,
                 time_filter: Dict = None):
        self.config = config
        self.logger = logger
        self.display_name = display_name
        self.local_tz = local_tz
        self.enable_sound = enable_sound
        self.time_filter = time_filter
        self.active_positions: Dict[str, dict] = {}
        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0
        self.skipped_signals = 0  # 统计因时间过滤跳过的信号数
        
        self.db_manager = db_manager or DatabaseManager()
        self.coin_symbol = display_name.split('/')[0] + 'USDT'
        
        self.risk_per_order_pct = float(self.config.get('risk_per_order_pct', 1))
        self.max_active_positions = int(self.config.get('max_active_positions', 5))
        self.paper_usdt_balance = float(self.config.get('paper_usdt_balance', 1000.0))
        self.logger.info(f"risk_per_order_pct {self.risk_per_order_pct},max_active_positions {self.max_active_positions}")
        
        self.order_manager = OrderManager(logger=self.logger, db_manager=self.db_manager, config=self.config)
        self.logger.info(f"livemode {self.order_manager.live_trading}")
        if self.order_manager.live_trading:
            leverage = int(self.config.get('futures_leverage', 20))
            try:
                self.order_manager.set_symbol_leverage(self.coin_symbol, leverage)
                self.logger.info(f"设置 {self.coin_symbol} 杠杆为 {leverage}x")
            except Exception as e:
                self.logger.warning(f"设置杠杆失败: {e}")
        
        # 当前K线相关变量，用于抵消逻辑
        self.current_kline_timestamp = None
        self.pending_orders = []  # 当前K线周期内的待处理订单
        self.pending_exits = []   # 当前K线周期内的待平仓仓位
        self.kline_close_processing = False  # 标记是否在K线收盘处理中

    def is_good_time_to_trade(self, timestamp):
        """检查当前时间是否适合开单"""
        if self.time_filter is None:
            return True
        
        # 转换为北京时间
        if hasattr(timestamp, 'astimezone'):
            beijing_time = timestamp.astimezone(self.local_tz)
        else:
            # 如果是pandas.Timestamp，先转换为datetime
            if hasattr(timestamp, 'to_pydatetime'):
                dt = timestamp.to_pydatetime()
            else:
                dt = timestamp
            
            # 确保有时区信息
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            
            beijing_time = dt.astimezone(self.local_tz)
        
        day_name = beijing_time.strftime('%A')
        hour = beijing_time.hour
        
        return day_name in self.time_filter and hour in self.time_filter[day_name]

    def log_message(self, event_type: str, message: str, data: dict = None, level: str = 'info'):
        log_entry = f"[{event_type}] {message}"
        if data:
            log_entry += f" | 数据: {json.dumps({k: (f'{v:.4f}' if isinstance(v, float) else v) for k, v in data.items()})}"
        getattr(self.logger, level, self.logger.info)(log_entry)

    def play_alert_sound(self, message):
        if not self.enable_sound: return
        try:
            if platform.system().lower() == "darwin": os.system(f'say "{message}"')
            else: print('\a' * 3) # Simple bell for other systems
        except Exception as e:
            self.logger.warning(f"播放报警声失败: {e}")

    def on_new_signal(self, signal: Dict):
        """
        处理新信号：先创建仓位记录，再添加到待处理订单队列
        """
        # 检查时间过滤
        if not self.is_good_time_to_trade(signal['timestamp']):
            self.skipped_signals += 1
            beijing_time = signal['timestamp'].astimezone(self.local_tz)
            self.log_message("时间过滤", 
                           f"跳过信号 - 当前时间不在允许交易时段 ({beijing_time.strftime('%A %H:%M')})", 
                           {'probability': signal['probability'], 'price': signal['price']}, 
                           level='info')
            return
        
        active_count = sum(1 for p in self.active_positions.values() if p['status'] in ['active', 'closing'])
        if active_count >= self.max_active_positions:
            self.log_message("风控", f"已达到最大活跃仓位数 {self.max_active_positions}，跳过新信号。", level='warning')
            return
        
        timestamp = signal['timestamp']
        position_id = f"pred_cn_{timestamp.astimezone(self.local_tz).strftime('%Y%m%d_%H%M%S')}"
        self.total_predictions += 1
        
        # 1. 创建仓位记录
        max_wait = pd.Timedelta(minutes=self.config['max_lookforward_minutes'])
        position_info = {
            'position_id': position_id,
            'signal_id': signal.get('signal_id', f"signal_{timestamp.strftime('%Y%m%d_%H%M%S')}"),
            'coin_symbol': self.coin_symbol,
            'signal_type': signal['guess'],
            'probability': signal['probability'],
            'up_target': signal['price'] * (1 + self.config['up_threshold']),
            'down_target': signal['price'] * (1 - self.config['down_threshold']),
            'expire_time': timestamp + max_wait,
            'status': 'pending_open',
            'metadata': {'display_name': self.display_name, 'signal_price': signal['price']}
        }
        
        side = OrderSide.BUY if signal['guess'] == 1 else OrderSide.SELL
        try:
            self.db_manager.insert_position(position_info, position_info['signal_id'])
            self.active_positions[position_id] = position_info
            self.log_message("新仓位意图", f"已创建仓位 {position_id},信心 {signal['probability']},方向: {side},price:{signal['price']}")
        except Exception as e:
            self.logger.error(f"初始仓位记录插入数据库失败: {e}。取消开仓。")
            return

        # 2. 添加到待处理开仓订单队列
        pending_order = {
            'type': 'entry',
            'position_id': position_id,
            'side': side,
            'signal_type': signal['guess'],
            'price': signal['price'],
            'timestamp': timestamp
        }
        self.pending_orders.append(pending_order)
        
        # 3. 处理当前K线的所有待处理订单（包括抵消逻辑）
        self._process_pending_orders()

    def on_price_update(self, current_price: float):
        if current_price is None: return
        now = datetime.now(timezone.utc)
        
        for pos_id in list(self.active_positions.keys()):
            pos = self.active_positions.get(pos_id)
            if not pos: continue

            # 处理closing状态的仓位
            if pos['status'] == 'closing':
                # closing状态：正在平仓中，不重复操作
                # 检查是否有超时情况
                if pos.get('closing_start_time'):
                    closing_duration = (now.timestamp() - pos['closing_start_time'].timestamp())
                    if closing_duration > 300:  # 5分钟超时
                        self.log_message("平仓超时", f"仓位 {pos_id} 平仓超时({closing_duration:.0f}秒)，标记为失败", level='warning')
                        self._update_position_status(pos_id, 'completed', {
                            'result': -3, 
                            'exit_reason': f'平仓超时({closing_duration:.0f}秒)'
                        })
                continue

            # 处理closing_failed状态的仓位（需要重试）
            if pos['status'] == 'closing_failed':
                # 检查重试次数限制
                if pos.get('exit_attempts', 0) >= 3:
                    self.log_message("平仓失败", f"仓位 {pos_id} 平仓重试次数已达上限，标记为失败", level='error')
                    self._update_position_status(pos_id, 'completed', {
                        'result': -3, 
                        'exit_reason': f'平仓重试失败，已尝试{pos.get("exit_attempts", 0)}次'
                    })
                    continue
                
                # 检查重试间隔
                last_retry_time = pos.get('last_retry_time')
                if last_retry_time and (now.timestamp() - last_retry_time.timestamp()) < 30:  # 30秒内不重试
                    continue
                
                self.log_message("平仓重试", f"重试平仓仓位 {pos_id} (第{pos.get('exit_attempts', 0) + 1}次)...", level='warning')
                self._retry_exit_order(pos_id, pos['pending_result'], current_price, now, pos['pending_reason'])
                continue

            # 检查活跃仓位
            if pos['status'] == 'active':
                should_exit = False
                result = None
                reason = None
                
                if current_price >= pos['up_target']:
                    result, reason = (1, "达到上涨目标") if pos['signal_type'] == 1 else (0, "达到上涨目标 (预测错误)")
                    should_exit = True
                elif current_price <= pos['down_target']:
                    result, reason = (1, "达到下跌目标") if pos['signal_type'] == 0 else (0, "达到下跌目标 (预测错误)")
                    should_exit = True
                elif self._is_expired(pos['expire_time'], now):
                    result, reason = -1, "超时"
                    should_exit = True
                
                if should_exit:
                    if self.kline_close_processing:
                        # 如果在K线收盘处理中，添加到待平仓队列
                        pending_exit = {
                            'type': 'exit',
                            'position_id': pos_id,
                            'signal_type': pos['signal_type'],
                            'result': result,
                            'price': current_price,
                            'reason': reason,
                            'timestamp': now
                        }
                        self.pending_exits.append(pending_exit)
                        self.logger.info(f"添加到平仓队列: {pos_id}, 原因: {reason}")
                    else:
                        # 否则立即执行平仓（原有逻辑）
                        self._complete_position(pos_id, result, current_price, now, reason)
        
        # 在K线收盘处理中不立即处理，等待统一处理
        # 注意：这里不调用_process_pending_orders()，等待finish_kline_processing()时统一处理

    def _complete_position(self, pos_id: str, result: int, final_price: float, end_time: datetime, reason: str):
        pos = self.active_positions.get(pos_id)
        if not pos or pos['status'] in ['completed', 'open_failed']: return

        # 1. 标记仓位为 'closing'，开始平仓流程
        self._update_position_status(pos_id, 'closing', {
            'pending_result': result, 
            'pending_reason': reason,
            'closing_start_time': end_time  # 记录开始平仓时间
        })

        # 2. 执行平仓订单
        self._execute_exit_order(pos_id, result, final_price, end_time, reason)

    def _execute_exit_order(self, pos_id: str, result: int, final_price: float, end_time: datetime, reason: str):
        """执行平仓订单"""
        pos = self.active_positions.get(pos_id)
        if not pos: return

        # 查找该仓位的开仓订单
        entry_orders = self.order_manager.get_orders_by_position(pos_id)
        entry_order = next((o for o in entry_orders if o.order_id == pos.get('entry_order_id')), None)
        if not entry_order or entry_order.filled_quantity <= 0:
            self.logger.warning(f"找不到仓位 {pos_id} 有效的开仓订单，无法平仓。")
            self._update_position_status(pos_id, 'completed', {'result': -2, 'exit_reason': 'Missing entry order'})
            return

        exit_side = OrderSide.SELL if pos['signal_type'] == 1 else OrderSide.BUY
        exit_order = self.order_manager.execute_market_order(
            position_id=pos_id, coin_symbol=self.coin_symbol, side=exit_side,
            quantity=entry_order.filled_quantity, price_hint=final_price,
            position_side=('LONG' if exit_side == OrderSide.SELL else 'SHORT')
        )
        
        # 根据平仓订单结果更新仓位状态
        if exit_order.status == OrderStatus.FILLED:
            self.logger.info(f"仓位 {pos_id} 的平仓订单 {exit_order.order_id} 成功成交。")
            filled_exit_price = exit_order.filled_price
            pnl = ((filled_exit_price - pos['entry_price']) / pos['entry_price']) if pos['signal_type'] == 1 else ((pos['entry_price'] - filled_exit_price) / pos['entry_price'])
            
            update_data = {
                'status': 'completed', 'exit_price': filled_exit_price, 'exit_time': exit_order.filled_time,
                'result': result, 'pnl': pnl, 'exit_reason': reason, 'exit_order_id': exit_order.order_id
            }
            self._update_position_status(pos_id, 'completed', update_data)
            
            # 更新统计
            if result == 1: self.successful_predictions += 1
            elif result == 0: self.failed_predictions += 1
            else: self.timeout_predictions += 1
            
            self.log_message("仓位完成", f"ID: {pos_id}, 结果: {('成功✅' if result == 1 else '失败❌' if result == 0 else '超时⏰')}", {
                'pnl': f"{pnl*100:.2f}%", 'reason': reason
            })
        else:
            self.logger.error(f"仓位 {pos_id} 的平仓订单 {exit_order.order_id} 失败，状态: {exit_order.status.value}。标记为需要重试。")
            # 标记为closing_failed，等待重试
            self._update_position_status(pos_id, 'closing_failed', {
                'exit_attempts': 1,
                'last_retry_time': end_time
            })

    def _is_expired(self, expire_time, current_time):
        """
        检查仓位是否过期
        智能处理pandas.Timestamp和datetime类型
        """
        try:
            # 如果是pandas.Timestamp，转换为datetime
            if hasattr(expire_time, 'to_pydatetime'):
                expire_dt = expire_time.to_pydatetime()
            else:
                expire_dt = expire_time
            
            return current_time >= expire_dt
        except Exception as e:
            self.logger.error(f"检查过期时间失败: {e}, expire_time: {expire_time}, current_time: {current_time}")
            return False

    def _retry_exit_order(self, pos_id: str, result: int, final_price: float, end_time: datetime, reason: str):
        """重试平仓订单"""
        pos = self.active_positions.get(pos_id)
        if not pos: return

        # 增加重试计数
        current_attempts = pos.get('exit_attempts', 0) + 1
        self._update_position_status(pos_id, 'closing_failed', {
            'exit_attempts': current_attempts,
            'last_retry_time': end_time
        })

        # 重新执行平仓
        self._execute_exit_order(pos_id, result, final_price, end_time, reason)

    def _update_position_status(self, pos_id: str, new_status: str, data: Dict):
        """
        统一的仓位更新函数，同时更新内存和数据库。
        """
        pos = self.active_positions.get(pos_id)
        if not pos: return
        
        pos.update(data)
        pos['status'] = new_status
        
        try:
            self.db_manager.update_position(pos_id, data)
        except Exception as e:
            self.logger.error(f"更新数据库仓位 {pos_id} 失败: {e}")
        
        # 如果仓位已终结，从活跃列表中移除
        if new_status in ['completed', 'open_failed']:
            if pos_id in self.active_positions:
                del self.active_positions[pos_id]


    def print_status(self):
        """Prints a summary of the performance."""
        # 从数据库获取最新统计以确保准确性
        stats = self.db_manager.get_trading_statistics(self.coin_symbol, days=30)
        
        active_count = len(self.active_positions)
        closing_count = sum(1 for p in self.active_positions.values() if p['status'] == 'closing')
        
        time_filter_info = ""
        if self.time_filter and self.skipped_signals > 0:
            time_filter_info = f"  时间过滤跳过信号: {self.skipped_signals}\n"
        
        self.logger.info("\n--- 状态摘要 ---\n"
                        f"  活跃仓位: {active_count - closing_count}, 平仓中: {closing_count}\n"
                        f"  总完成仓位 (最近30天): {stats.get('total_positions', 0)}\n"
                        f"  成功: {stats.get('successful_positions', 0)}, 失败: {stats.get('failed_positions', 0)}, 超时: {stats.get('timeout_positions', 0)}\n"
                        f"  胜率 (非超时): {stats.get('success_rate', 0):.2f}%\n"
                        f"  总盈亏 (最近30天): {stats.get('total_pnl', 0) * 100:.2f}%\n"
                        f"{time_filter_info}"
                        "------------------")

    def handle_offset_position(self, position_id: str, exit_price: float, exit_time: datetime, 
                              result: int, pnl: float, exit_reason: str):
        """
        处理被信号抵消的仓位
        这个方法由SignalGenerator调用，用于更新被抵消仓位的状态
        """
        pos = self.active_positions.get(position_id)
        if not pos:
            self.logger.warning(f"尝试抵消不存在的仓位: {position_id}")
            return False
        
        if pos['status'] != 'active':
            self.logger.warning(f"仓位 {position_id} 状态不是active，无法抵消 (当前状态: {pos['status']})")
            return False
        
        # 更新仓位状态
        update_data = {
            'status': 'completed',
            'exit_price': exit_price,
            'exit_time': exit_time,
            'result': result,
            'pnl': pnl,
            'exit_reason': exit_reason,
            'exit_order_id': f"offset_{int(exit_time.timestamp())}"
        }
        
        self._update_position_status(position_id, 'completed', update_data)
        
        # 更新统计
        if result == 1:
            self.successful_predictions += 1
        elif result == 0:
            self.failed_predictions += 1
        else:
            self.timeout_predictions += 1
        
        self.log_message("仓位抵消", f"ID: {position_id}, 结果: {('成功✅' if result == 1 else '失败❌')}", {
            'pnl': f"{pnl*100:.2f}%", 'reason': exit_reason
        })
        
        return True

    def on_kline_close(self, current_price: float, signal: Optional[Dict] = None):
        """
        K线收盘时的统一处理：同时处理平仓需求和新信号
        """
        self.kline_close_processing = True
        
        # 清空之前的待处理队列
        self.pending_orders.clear()
        self.pending_exits.clear()
        
        # 1. 收集需要平仓的仓位
        self.on_price_update(current_price)
        
        # 2. 处理新信号（如果有）
        if signal:
            self.on_new_signal(signal)
        
        # 3. 处理所有待处理订单（包括抵消逻辑）
        if self.pending_orders or self.pending_exits:
            self._process_pending_orders()
        
        # 重置K线处理状态
        self.kline_close_processing = False

    def _process_pending_orders(self):
        """
        处理当前时间点的所有待处理订单，执行抵消逻辑
        """
        if not self.pending_orders and not self.pending_exits:
            return
        
        self.logger.info(f"处理待处理订单: {len(self.pending_orders)}个开仓, {len(self.pending_exits)}个平仓")
        
        # 尝试抵消：找到方向相反的开仓和平仓订单
        offset_pairs = []
        remaining_entries = self.pending_orders.copy()
        remaining_exits = self.pending_exits.copy()
        
        for entry_order in self.pending_orders:
            for exit_order in self.pending_exits:
                # 检查是否方向相反（开仓买入 vs 平仓卖出，或开仓卖出 vs 平仓买入）
                entry_side = entry_order['side']
                pos = self.active_positions.get(exit_order['position_id'])
                if not pos:
                    continue
                    
                exit_side = OrderSide.SELL if pos['signal_type'] == 1 else OrderSide.BUY
                
                # 如果开仓和平仓方向相反，可以抵消
                # 例如：平仓卖出 + 开仓买入，或平仓买入 + 开仓卖出
                if ((entry_side == OrderSide.BUY and exit_side == OrderSide.SELL) or 
                    (entry_side == OrderSide.SELL and exit_side == OrderSide.BUY)):
                    
                    offset_pairs.append((entry_order, exit_order))
                    if entry_order in remaining_entries:
                        remaining_entries.remove(entry_order)
                    if exit_order in remaining_exits:
                        remaining_exits.remove(exit_order)
                    break  # 每个开仓订单只能抵消一个平仓订单
        
        # 执行抵消操作
        for entry_order, exit_order in offset_pairs:
            self._execute_offset(entry_order, exit_order)
        
        # 执行剩余的订单
        for entry_order in remaining_entries:
            self._execute_entry_order(entry_order)
            
        for exit_order in remaining_exits:
            self._execute_exit_order_direct(exit_order)
        
        # 清空待处理队列
        self.pending_orders.clear()
        self.pending_exits.clear()

    def _execute_offset(self, entry_order: Dict, exit_order: Dict):
        """
        执行抵消操作：平仓老仓位，新仓位变为active（相当于持仓转移）
        """
        entry_pos_id = entry_order['position_id']
        exit_pos_id = exit_order['position_id']
        
        # 从数据库获取最新的仓位信息
        with self.db_manager.get_connection() as conn:
            df = pd.read_sql_query(
                "SELECT * FROM positions WHERE position_id = ?", 
                conn, params=[exit_pos_id]
            )
        
        if df.empty:
            self.logger.error(f"抵消失败：找不到平仓仓位 {exit_pos_id}")
            return
        
        exit_pos = df.iloc[0].to_dict()
        
        # 调试信息
        self.logger.info(f"抵消调试：仓位 {exit_pos_id} 数据: entry_price={exit_pos.get('entry_price')}, status={exit_pos.get('status')}")
        
        # 计算平仓仓位的盈亏
        old_entry_price = exit_pos.get('entry_price')
        if old_entry_price is None or pd.isna(old_entry_price):
            self.logger.error(f"抵消失败：仓位 {exit_pos_id} 没有开仓价格 (entry_price={old_entry_price})")
            return
            
        exit_price = exit_order['price']
        
        if exit_pos['signal_type'] == 1:  # 原来是看涨
            pnl = (exit_price - old_entry_price) / old_entry_price
        else:  # 原来是看跌
            pnl = (old_entry_price - exit_price) / old_entry_price
        
        # 1. 更新平仓仓位为已完成
        exit_update_data = {
            'status': 'completed',
            'exit_price': exit_price,
            'exit_time': exit_order['timestamp'],
            'result': exit_order['result'],
            'pnl': pnl,
            'exit_reason': f"订单抵消平仓 - {exit_order['reason']}",
            'exit_order_id': f"offset_exit_{int(exit_order['timestamp'].timestamp())}"
        }
        self._update_position_status(exit_pos_id, 'completed', exit_update_data)
        
        # 2. 更新新仓位为active（相当于真的开仓了）
        new_entry_price = entry_order['price']  # 新仓位的开仓价格
        
        # 创建虚拟的开仓订单记录
        virtual_order_id = f"offset_transfer_{int(entry_order['timestamp'].timestamp())}"
        
        entry_update_data = {
            'status': 'active',
            'entry_price': new_entry_price,
            'entry_time': entry_order['timestamp'],
            'entry_order_id': virtual_order_id,
            # 重新计算新仓位的目标价格
            'up_target': new_entry_price * (1 + self.config['up_threshold']),
            'down_target': new_entry_price * (1 - self.config['down_threshold'])
        }
        self._update_position_status(entry_pos_id, 'active', entry_update_data)
        
        # 获取老仓位的实际数量（从其开仓订单中获取）
        old_quantity = 20.0  # 默认值
        try:
            old_entry_orders = self.order_manager.get_orders_by_position(exit_pos_id)
            old_entry_order = next((o for o in old_entry_orders if o.order_id == exit_pos.get('entry_order_id')), None)
            if old_entry_order and old_entry_order.filled_quantity > 0:
                old_quantity = old_entry_order.filled_quantity
                self.logger.info(f"获取老仓位数量: {old_quantity}")
            else:
                self.logger.warning(f"无法获取老仓位 {exit_pos_id} 的数量，使用默认值 {old_quantity}")
        except Exception as e:
            self.logger.error(f"获取老仓位数量失败: {e}，使用默认值 {old_quantity}")
        
        # 创建虚拟订单记录，使用老仓位的相同数量
        from order_manager import Order, OrderType, OrderSide, OrderStatus
        virtual_order = Order(entry_pos_id, self.coin_symbol, OrderType.MARKET, 
                             entry_order['side'], old_quantity, new_entry_price)  # 使用老仓位的数量
        virtual_order.order_id = virtual_order_id
        virtual_order.status = OrderStatus.FILLED
        virtual_order.filled_quantity = old_quantity  # 使用老仓位的相同数量
        virtual_order.filled_price = new_entry_price
        virtual_order.commission = 0.0  # 抵消操作无手续费
        virtual_order.filled_time = entry_order['timestamp']
        
        # 保存虚拟订单到数据库
        try:
            self.order_manager._save_order_to_db(virtual_order)
            # 同时添加到内存中
            self.order_manager.active_orders[virtual_order_id] = virtual_order
        except Exception as e:
            self.logger.error(f"保存虚拟订单失败: {e}")
        
        # 3. 更新统计（只统计平仓的老仓位）
        if exit_order['result'] == 1:
            self.successful_predictions += 1
        elif exit_order['result'] == 0:
            self.failed_predictions += 1
        else:
            self.timeout_predictions += 1
        
        self.log_message("订单抵消", 
                        f"持仓转移: {exit_pos_id}(平仓) → {entry_pos_id}(新开仓)", {
                            'old_entry_price': f"{old_entry_price:.4f}",
                            'new_entry_price': f"{new_entry_price:.4f}",
                            'exit_pnl': f"{pnl*100:.2f}%",
                            'exit_result': ('成功✅' if exit_order['result'] == 1 else '失败❌' if exit_order['result'] == 0 else '超时⏰'),
                            'saved_fees': '避免2笔手续费'
                        })

    def _execute_entry_order(self, entry_order: Dict):
        """
        执行正常的开仓订单
        """
        position_id = entry_order['position_id']
        side = entry_order['side']
        price = entry_order['price']
        
        quantity = self.order_manager.compute_order_quantity(
            symbol=self.coin_symbol, price=price, risk_per_order_pct=self.risk_per_order_pct
        )
        
        if quantity <= 0:
            self.logger.warning(f"计算的下单数量为0，仓位 {position_id} 开仓失败。")
            self._update_position_status(position_id, 'open_failed', {'exit_reason': 'Zero quantity'})
            return

        order = self.order_manager.execute_market_order(
            position_id=position_id, coin_symbol=self.coin_symbol, side=side, quantity=quantity,
            price_hint=price, position_side=('LONG' if side == OrderSide.BUY else 'SHORT')
        )

        # 根据订单结果更新仓位状态
        update_data = {'entry_order_id': order.order_id}
        if order.status == OrderStatus.FILLED:
            self.logger.info(f"💚仓位 {position_id} 的开仓订单 {order.order_id} 成功成交。{side}:{price}->{order.filled_price}")
            update_data['status'] = 'active'
            update_data['entry_price'] = order.filled_price
            update_data['entry_time'] = order.filled_time
            # 使用真实成交价重新计算目标
            update_data['up_target'] = order.filled_price * (1 + self.config['up_threshold'])
            update_data['down_target'] = order.filled_price * (1 - self.config['down_threshold'])
            
            direction = "看涨" if entry_order['signal_type'] == 1 else "看跌"
            self.play_alert_sound(f"{self.display_name} {direction} 开仓成功")
        else:
            self.logger.error(f"仓位 {position_id} 的开仓订单 {order.order_id} 失败，状态: {order.status.value}。")
            update_data['status'] = 'open_failed'
            update_data['exit_reason'] = f'Entry order failed with status: {order.status.value}'
        
        self._update_position_status(position_id, update_data['status'], update_data)

    def _execute_exit_order_direct(self, exit_order: Dict):
        """
        直接执行平仓订单（原有逻辑）
        """
        pos_id = exit_order['position_id']
        result = exit_order['result']
        final_price = exit_order['price']
        end_time = exit_order['timestamp']
        reason = exit_order['reason']
        
        self._complete_position(pos_id, result, final_price, end_time, reason)