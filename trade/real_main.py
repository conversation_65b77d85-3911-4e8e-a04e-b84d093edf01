# main.py

import argparse
import os
import sys
import logging
from datetime import datetime
import json
import pytz

# Assume these utility functions are in a shared file.
from model_utils_815 import get_coin_config, get_output_dir

# Import the three core modules with their new names.
from real_data import MarketDataFetcher
from real_signal import SignalGenerator
from real_manager import PortfolioManager
from database_manager import DatabaseManager

def setup_logging(log_file: str, coin_name: str) -> logging.Logger:
    """Sets up the logger to output to both console and file."""
    logger = logging.getLogger(f'{coin_name}Predictor')
    logger.setLevel(logging.INFO)
    logger.propagate = False
    if logger.hasHandlers():
        logger.handlers.clear()
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # File Handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Console Handler
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)
    return logger

def load_chushou_config(chushou_file='chushou.json'):
    """加载出手时间配置"""
    if chushou_file is None or not os.path.exists(chushou_file):
        if chushou_file is not None:
            print(f"⚠️ 出手时间配置文件 '{chushou_file}' 不存在，将在所有时间开单")
        return None
    
    try:
        with open(chushou_file, 'r', encoding='utf-8') as f:
            chushou_config = json.load(f)
        
        # 转换为更易查询的格式
        time_filter = {}
        for day_config in chushou_config:
            day_name = day_config['StartDayName']
            hours = day_config['StartHour']
            time_filter[day_name] = set(hours)
        
        print(f"✅ 已加载出手时间配置，将仅在指定时间段开单")
        for day, hours in time_filter.items():
            print(f"  {day}: {sorted(list(hours))} 时")
        
        return time_filter
    except Exception as e:
        print(f"❌ 加载出手时间配置失败: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="分层式多币种实时预测器")
    parser.add_argument("--coin", default="DOT", help="币种名称 (如: DOT, SEI)")
    parser.add_argument("--update-interval", type=int, default=60, help="数据获取和预测的更新间隔（秒）")
    parser.add_argument("--no-sound", action="store_true", help="禁用报警声")
    parser.add_argument("--use-chushou", action='store_true', help="启用出手时间过滤，仅在高胜率时间段开单")
    parser.add_argument("--chushou-file", default="chushou.json", help="出手时间配置文件路径 (默认: chushou.json)")
    args = parser.parse_args()

    try:
        # --- 1. Initialize configuration and logging ---
        coin_name = args.coin.upper()
        coin_config = get_coin_config(coin_name)
        if coin_config is None:
            raise ValueError(f"无法获取币种 {coin_name} 的配置")

        local_tz = pytz.timezone('Asia/Shanghai')
        output_dir = get_output_dir()
        model_file = os.path.join(output_dir, f"{coin_config['model_basename']}_model.joblib")
        config_file = os.path.join(output_dir, f"{coin_config['model_basename']}_config.json")
        log_file = os.path.join(output_dir, f"{coin_name.lower()}_prediction_log.txt")

        if not os.path.exists(model_file) or not os.path.exists(config_file):
            print(f"❌ 错误: 模型文件 '{model_file}' 或配置文件 '{config_file}' 不存在。")
            return

        logger = setup_logging(log_file, coin_name)
        
        # Log initial setup information
        logger.info("="*20 + f" {coin_config['display_name']} 预测器启动 (简化版) " + "="*20)
        logger.info(f"币种: {coin_config['display_name']}")
        logger.info(f"API符号: {coin_config['api_symbol']}")
        logger.info(f"模型文件: {model_file}")
        logger.info(f"配置文件: {config_file}")
        logger.info(f"日志文件: {log_file}")
        logger.info(f"更新间隔: {args.update_interval}秒")
        logger.info(f"报警声: {'启用' if not args.no_sound else '禁用'}")
        
        # 加载出手时间配置
        time_filter = None
        if args.use_chushou:
            time_filter = load_chushou_config(args.chushou_file)
            if time_filter:
                logger.info(f"出手时间过滤: 启用 (配置文件: {args.chushou_file})")
            else:
                logger.warning("出手时间过滤启用失败，将在所有时间开单")
        else:
            logger.info("出手时间过滤: 禁用")
        
        logger.info("按 Ctrl+C 停止预测\n")
        
        with open(config_file, 'r') as f:
            loaded_config = json.load(f)

        # --- 2. Initialize database manager ---
        db_file = os.path.join(output_dir, f"{coin_name.lower()}_trading_system.db")
        db_manager = DatabaseManager(db_file)
        logger.info(f"数据库文件: {db_file}")

        # --- 3. Instantiate the three modules, passing necessary info ---
        portfolio_mgr = PortfolioManager(
            config=loaded_config, logger=logger, display_name=coin_config['display_name'],
            local_tz=local_tz, enable_sound=not args.no_sound, db_manager=db_manager,
            time_filter=time_filter
        )
        
        signal_gen = SignalGenerator(
            model_file=model_file, config_file=config_file, logger=logger,
            display_name=coin_config['display_name'], local_tz=local_tz, db_manager=db_manager
        )
        
        market_fetcher = MarketDataFetcher(
            api_symbol=coin_config['api_symbol'], timeframe_minutes=coin_config['timeframe_minutes'],
            update_interval=args.update_interval, logger=logger,
            display_name=coin_config['display_name'], local_tz=local_tz, db_manager=db_manager
        )

        # --- 4. Register callbacks to connect the modules ---
        # 注册处理器
        # price_update_handler 是可选的，如果不传入则只在K线收盘时处理
        signal_gen.register_handlers(
            signal_handler=portfolio_mgr.on_new_signal,
            # price_update_handler=portfolio_mgr.on_price_update,  # 可选：实时价格更新
            portfolio_manager=portfolio_mgr
        )
        market_fetcher.register_kline_handler(
            handler=signal_gen.on_new_kline_data
        )

        # --- 5. Start the program ---
        logger.info("启动主预测循环...")
        market_fetcher.run()

    except (ValueError, RuntimeError) as e:
        print(f"❌ 启动错误: {e}")
        return
    finally:
        if 'portfolio_mgr' in locals():
            print("\n" + "="*20 + " 实时预测总结 " + "="*20)
            portfolio_mgr.print_status()
            logger.info("[系统] 预测器已停止")
        print("程序已退出。")

if __name__ == "__main__":
    main()